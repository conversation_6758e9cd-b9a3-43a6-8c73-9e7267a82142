package service

import (
	"deployment/common"
	"deployment/logger"
	"deployment/repository"
	"deployment/rest"
	"deployment/rest/view"
	"encoding/json"
)

type AssetPatchCveRelationService struct {
	Repository *repository.AssetPatchCveRelationRepository
}

type JsonObject map[string]interface{}
type JsonArray []interface{}

func getStringValue(value interface{}) string {
	if str, ok := value.(string); ok {
		return str
	}
	return ""
}

func NewAssetPatchCveRelationService() *AssetPatchCveRelationService {
	return &AssetPatchCveRelationService{
		Repository: repository.NewAssetPatchCveRelationRepository(),
	}
}

func (service AssetPatchCveRelationService) convertToModel(restModel view.AssetPatchCveRelationRest) *view.AssetPatchCveRelation {
	marshal, _ := json.Marshal(restModel.CveContext)
	return &view.AssetPatchCveRelation{
		PatchName:        restModel.PatchName,
		KbId:             restModel.KbId,
		SoftwareId:       restModel.SoftwareId,
		Cve:              restModel.Cve,
		Severity:         restModel.Severity,
		Name:             restModel.Name,
		Version:          restModel.Version,
		Score:            restModel.Score,
		Cvss3BaseScore:   restModel.Cvss3BaseScore,
		Cvss2BaseScore:   restModel.Cvss2BaseScore,
		CveContext:       string(marshal),
		EpssProbability:  restModel.EpssProbability,
		CisaKnownExploit: restModel.CisaKnownExploit,
		DiscoveredTime:   restModel.DiscoveredTime,
		AssetId:          restModel.AssetId,
		Archived:         restModel.Archived,
		HasPatch:         restModel.HasPatch,
	}
}
func (service AssetPatchCveRelationService) convertToRest(cveRelation view.AssetPatchCveRelation) view.AssetPatchCveRelationRest {
	var contextMap map[string]interface{}
	err := json.Unmarshal([]byte(cveRelation.CveContext), &contextMap)
	if err != nil {
		logger.ServiceLogger.Error("[convertToRest] CveContext Unmarshal Error:", err)
	}
	return view.AssetPatchCveRelationRest{
		PatchName:        cveRelation.PatchName,
		KbId:             cveRelation.KbId,
		SoftwareId:       cveRelation.SoftwareId,
		Cve:              cveRelation.Cve,
		Severity:         cveRelation.Severity,
		Name:             cveRelation.Name,
		Version:          cveRelation.Version,
		Score:            cveRelation.Score,
		Cvss3BaseScore:   cveRelation.Cvss3BaseScore,
		Cvss2BaseScore:   cveRelation.Cvss2BaseScore,
		CveContext:       contextMap,
		EpssProbability:  cveRelation.EpssProbability,
		CisaKnownExploit: cveRelation.CisaKnownExploit,
		DiscoveredTime:   cveRelation.DiscoveredTime,
		AssetId:          cveRelation.AssetId,
		Archived:         cveRelation.Archived,
		HasPatch:         cveRelation.HasPatch,
	}
}

func (service AssetPatchCveRelationService) GetByCve(cve string) (map[string]interface{}, error) {
	query := "SELECT * FROM deployment." + common.VIEW_ASSET_PATCH_CVE_RELATION.String() + " WHERE cve = '?'"
	parameters := make([]interface{}, 0)
	parameters = append(parameters, cve)
	relation, err := service.Repository.GetCveDetail(query, parameters)
	if err != nil {
		logger.ServiceLogger.Info("Error while cve : ", cve, " Error :", err.Error())
		return relation, nil
	}
	cveContextObject, hasCveContext := relation["cve_context"]
	delete(relation, "cve_context")
	if hasCveContext {
		var cveContext map[string]interface{}
		if err := json.Unmarshal([]byte(cveContextObject.(string)), &cveContext); err == nil {
			if nvdContext, ok := cveContext["nvd_context"].(map[string]interface{}); ok {
				for k, v := range nvdContext {
					relation[k] = v
				}
			} else {
				for k, v := range cveContext {
					relation[k] = v
				}
			}
		}
	}
	return relation, nil
}

func (service AssetPatchCveRelationService) GetAllPatchCveRelation(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	filter.IncludeArchived = true
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.VIEW_ASSET_PATCH_CVE_RELATION.String(), true, "")
	var responsePage rest.ListResponseRest
	var relations []map[string]interface{}
	var result []map[string]interface{}
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		filter.Columns = "COUNT(DISTINCT asset_id) AS asset_count,COUNT(DISTINCT software_id) AS software_count,cve,severity,score,cvss3_base_score,cvss2_base_score,published_date,cvss40_base_score,epss_probability,cisa_known_exploit,description,kb_article,kb_article_url,cve_context,mitre_threat,threat_intel_details,cvss2_vector,cvss2_access_vector,cvss2_access_complexity,cvss2_authentication,cvss2_confidentiality_impact,cvss2_integrity_impact,cvss2_availability_impact,cvss2_severity,cvss2_exploitability_score,cvss2_impact_score,cvss3_vector_string,cvss3_attack_vector,cvss3_attack_complexity,cvss3_privileges_required,cvss3_scope,cvss3_confidentiality_impact,cvss3_integrity_impact,cvss3_availability_impact,cvss3_base_severity,cvss3_exploitability_score,cvss3_impact_score,cvss40_source,cvss40_type,cvss40_vector_string,cvss40_base_severity,cvss40_threat_score,cvss40_threat_severity,cvss40_environmental_score,cvss40_environmental_severity"
		filter.GroupBy = "cve,severity,score,cvss3_base_score,cvss2_base_score,published_date,cvss40_base_score,epss_probability,cisa_known_exploit,description,kb_article,kb_article_url,cve_context,mitre_threat,threat_intel_details,cvss2_vector,cvss2_access_vector,cvss2_access_complexity,cvss2_authentication,cvss2_confidentiality_impact,cvss2_integrity_impact,cvss2_availability_impact,cvss2_severity,cvss2_exploitability_score,cvss2_impact_score,cvss3_vector_string,cvss3_attack_vector,cvss3_attack_complexity,cvss3_privileges_required,cvss3_scope,cvss3_confidentiality_impact,cvss3_integrity_impact,cvss3_availability_impact,cvss3_base_severity,cvss3_exploitability_score,cvss3_impact_score,cvss40_source,cvss40_type,cvss40_vector_string,cvss40_base_severity,cvss40_threat_score,cvss40_threat_severity,cvss40_environmental_score,cvss40_environmental_severity"
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.VIEW_ASSET_PATCH_CVE_RELATION.String(), false, "")
		relations, err = service.Repository.GetDetails(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		for _, row := range relations {
			cveContextObject, hasCveContext := row["cve_context"]
			delete(row, "cve_context")
			if hasCveContext {
				var cveContext map[string]interface{}
				if err := json.Unmarshal([]byte(cveContextObject.(string)), &cveContext); err == nil {
					if nvdContext, ok := cveContext["nvd_context"].(map[string]interface{}); ok {
						for k, v := range nvdContext {
							row[k] = v
						}
					} else {
						for k, v := range cveContext {
							row[k] = v
						}
					}
				}
			}
			threatIntelObject, hasThreatIntel := row["threat_intel_details"]
			if hasThreatIntel && threatIntelObject != nil {
				row["threat_intel_details"] = prepareThreatIntelDetails(threatIntelObject)
			}
			result = append(result, row)
		}
		responsePage.ObjectList = result
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service AssetPatchCveRelationService) convertListToRest(relations []view.AssetPatchCveRelation) []map[string]interface{} {
	var relationRests []map[string]interface{}
	if len(relations) != 0 {
		for _, patch := range relations {
			patchRest := service.convertToRest(patch)
			ctx := patchRest.CveContext
			var context map[string]interface{}
			marshal, _ := json.Marshal(patchRest)
			err := json.Unmarshal(marshal, &context)
			if err != nil {
				logger.ServiceLogger.Error("[convertListToRest] CveContext Unmarshal Error:", err)
			}
			for k, v := range ctx {
				context[k] = v
			}
			relationRests = append(relationRests, context)
		}
	}
	return relationRests
}

func prepareThreatIntelDetails(threatIntel interface{}) JsonObject {
	threatIntelArray, ok := threatIntel.(JsonArray)
	if !ok {
		return JsonObject{}
	}

	result := JsonObject{}

	if len(threatIntelArray) > 0 {
		for _, item := range threatIntelArray {
			technique, ok := item.(JsonObject)
			if !ok {
				continue
			}

			if killChainPhases, exists := technique["kill_chain_phases"].(JsonArray); exists {
				delete(technique, "kill_chain_phases")

				parentTechnique := getStringValue(technique["paren_technique_name"])
				delete(technique, "paren_technique_name")

				for _, phase := range killChainPhases {
					jsonObject, ok := phase.(JsonObject)
					if !ok {
						continue
					}

					if tactic, exists := jsonObject["tactic"].(string); exists {
						if _, found := result[tactic]; !found {
							result[tactic] = JsonObject{}
						}

						if parentTechnique == "" {
							parentTechnique = getStringValue(technique["name"])
						}

						parentMap, _ := result[tactic].(JsonObject)
						if _, found := parentMap[parentTechnique]; !found {
							parentMap[parentTechnique] = JsonArray{}
						}

						parentArray, _ := parentMap[parentTechnique].(JsonArray)
						parentArray = append(parentArray, technique)
						parentMap[parentTechnique] = parentArray
						result[tactic] = parentMap
					}
				}
			}
		}
	}

	return result
}
